'use client'

import { useState, useEffect, Suspense, lazy } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Cell } from './Cell'
import { Button } from "@/components/ui/button"
import { Plus, Database, ChevronDown, Code2, FileText } from "lucide-react"
import { nanoid } from 'nanoid'
import { toast } from 'sonner'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { NotebookExporter } from './NotebookExporter'
import { SavedChart, TableItem, PythonPlotItem } from './DashboardSection/types' // Import for type compatibility
import { Dataset } from '@/types/index'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useDashboardStore } from '@/lib/dashboardStore'
import { MiniSidebar } from '@/components/Datanalytics/DataEditorComponents/MiniSidebar'
import dynamic from 'next/dynamic'

// Lazy load the Dashboard component for better performance
// Use a simple loading state
const Dashboard = dynamic(() => import('./DashboardSection/Dashboard').then(mod => mod.default), {
  loading: () => (
    <div className="flex items-center justify-center h-[calc(100vh-100px)]">
      <div className="text-center">
        <div className="text-lg font-medium mb-2">Loading Dashboard...</div>
        <div className="text-sm text-muted-foreground">Please wait</div>
      </div>
    </div>
  ),
  ssr: false // Disable server-side rendering for this component
})

// Import logic components from chartbuilderlogic folder
import {
  useDatasetHandling,
  useCellExecution,
  useChartSaving,
  useDashboardInteraction,
  getDefaultContent,
  CellData,
  QueryResult
} from './chartbuilderlogic'

export default function ChartBuilder() {
  const [cells, setCells] = useState<CellData[]>([
    {
      id: nanoid(),
      content: '-- Write your SQL query here\nSELECT * FROM dataset1 LIMIT 5',
      language: 'sql',
      selectedDatasetIds: [] // Initialize with empty dataset selection
    }
  ])
  const [currentData, setCurrentData] = useState<QueryResult[]>([])

  // Use Zustand store for dashboard items
  const {
    charts: savedCharts,
    tables: savedTables,
    plots: savedPlots,
    calculatorResults: savedCalculatorResults
  } = useDashboardStore()

  const [isOnline, setIsOnline] = useState<boolean>(true) // Track online status
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false) // Track sidebar collapse state

  // Use URL search parameters for tab state
  const router = useRouter()
  const searchParams = useSearchParams()
  const tabParam = searchParams.get('tab')
  const [activeTab, setActiveTab] = useState<string>(tabParam === 'dashboard' ? 'dashboard' : 'notebook')

  // Sync activeTab with URL on mount and when URL changes
  useEffect(() => {
    const newTab = tabParam === 'dashboard' ? 'dashboard' : 'notebook';
    setActiveTab(newTab);
  }, [tabParam]);

  // Preload the Dashboard component when the component mounts
  useEffect(() => {
    // Simple preload without complex logic
    if (typeof window !== 'undefined') {
      // Use a short timeout to avoid blocking initial render
      setTimeout(() => {
        import('./DashboardSection/Dashboard').then(mod => {
          // Just preload, don't do anything with the result
          console.log('Dashboard component preloaded');
        }).catch(err => {
          // Silently handle any errors during preloading
          console.error('Error preloading Dashboard component:', err);
        });
      }, 500);
    }
  }, []);

  // Use the dataset handling hook
  const {
    datasets,
    datasetCache,
    isLoadingDatasets,
    handleSelectDatasets
  } = useDatasetHandling()

  // Use the cell execution hook
  const {
    handleRunCell,
    isAlasqlInitialized
  } = useCellExecution(setCells, cells, datasetCache)

  // Use the chart saving hook
  const {
    handleSaveChart,
    handleSaveTable,
    handleSavePlot,
    handleSaveCalculatorResult,
    handleRemoveChart,
    handleUpdateChart,
    handleReorderCharts
  } = useChartSaving()

  // Use the dashboard interaction hook
  const {
    handleNotebookImport,
    handleImportChartConfig
  } = useDashboardInteraction(setCells, cells, datasets, handleSelectDatasets)

  // Add online status detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      }
    };
  }, []);





  // Use the getDefaultContent function from chartbuilderlogic
  const getDefaultMarkdownContent = () => {
    return getDefaultContent('markdown');
  }

  // Update handleAddCell to support different cell types
  const handleAddCell = (afterId?: string, cellType: 'code' | 'markdown' = 'code') => {
    setCells(prev => {
      const newCell = {
        id: nanoid(),
        content: cellType === 'markdown' ? getDefaultMarkdownContent() : getDefaultContent('sql'),
        language: cellType === 'markdown' ? 'markdown' : 'sql',
        cellType: cellType,
        selectedDatasetIds: [] // Initialize with empty dataset selection
      }

      if (!afterId || prev.length === 0) {
        return [...prev, newCell]
      }

      const index = prev.findIndex(cell => cell.id === afterId)
      return [
        ...prev.slice(0, index + 1),
        newCell,
        ...prev.slice(index + 1)
      ]
    })
  }

  // Delete cell
  const handleDeleteCell = (cellId: string) => {
    setCells(prev => prev.filter(cell => cell.id !== cellId))
  }

  const handleLanguageChange = (cellId: string, language: string) => {
    setCells(prev => {
      const cell = prev.find(c => c.id === cellId);
      if (!cell) return prev;

      // Only replace content if it's empty or matches the default for the previous language
      const currentContent = cell.content || '';
      const defaultForOldLang = getDefaultContent(cell.language as any);
      const shouldReplaceContent = !currentContent.trim() ||
                                  currentContent === defaultForOldLang ||
                                  // Also replace if switching between languages and content has default markers
                                  (language === 'python' && currentContent.includes('-- Write your SQL query here')) ||
                                  (language === 'sql' && currentContent.includes('# Write your Python code here'));

      return prev.map(c =>
        c.id === cellId
          ? {
              ...c,
              language,
              content: shouldReplaceContent ? getDefaultContent(language as any) : c.content,
              // Clear any previous results and errors when changing language
              result: undefined,
              error: undefined,
              errorDetails: undefined,
              isSuccess: undefined
            }
          : c
      );
    });

    // Log the language change for debugging
    console.log(`Language changed for cell ${cellId} to ${language}`);
  }



  // Handle updating notes for a cell
  const handleUpdateNotes = (cellId: string, notes: string) => {
    setCells(prev => prev.map(cell =>
      cell.id === cellId ? { ...cell, notes } : cell
    ));

    // Save to localStorage or other persistence mechanism if needed
    toast.success("Notes updated");
  };

  // Handle drag and drop reordering of cells
  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    // If there's no destination or the item was dropped back in its original position
    if (!destination ||
        (destination.droppableId === source.droppableId &&
         destination.index === source.index)) {
      return;
    }

    // Reorder the cells array
    const reorderedCells = Array.from(cells);
    const [movedCell] = reorderedCells.splice(source.index, 1);
    reorderedCells.splice(destination.index, 0, movedCell);

    // Update the state with the new order
    setCells(reorderedCells);
    toast.success(`Cell moved to position ${destination.index + 1}`);
  };

  // Move a cell up in the order
  const moveCellUp = (cellId: string) => {
    const index = cells.findIndex(cell => cell.id === cellId);
    if (index <= 0) return; // Already at the top

    const reorderedCells = Array.from(cells);
    const [movedCell] = reorderedCells.splice(index, 1);
    reorderedCells.splice(index - 1, 0, movedCell);

    setCells(reorderedCells);
    toast.success(`Cell moved up`);
  };

  // Move a cell down in the order
  const moveCellDown = (cellId: string) => {
    const index = cells.findIndex(cell => cell.id === cellId);
    if (index === -1 || index >= cells.length - 1) return; // Already at the bottom

    const reorderedCells = Array.from(cells);
    const [movedCell] = reorderedCells.splice(index, 1);
    reorderedCells.splice(index + 1, 0, movedCell);

    setCells(reorderedCells);
    toast.success(`Cell moved down`);
  };

  // Add convertCellType function after moveCellDown function
  const convertCellType = (cellId: string, targetType: 'code' | 'markdown') => {
    setCells(prev => {
      // Find the cell
      const cellIndex = prev.findIndex(cell => cell.id === cellId);
      if (cellIndex === -1) return prev;

      const cell = prev[cellIndex];

      // If already the target type, do nothing
      if (cell.cellType === targetType) return prev;

      // Create default content for the new type
      let newContent = '';
      let newLanguage = cell.language;

      if (targetType === 'markdown') {
        // Convert code to markdown
        newContent = `# Code Snippet\n\`\`\`${cell.language}\n${cell.content}\n\`\`\``;
        newLanguage = 'markdown';
      } else {
        // Convert markdown to code
        newContent = getDefaultContent('sql');
        newLanguage = 'sql';
      }

      // Return updated cells array
      return prev.map((c, i) =>
        i === cellIndex
          ? {
              ...c,
              cellType: targetType,
              content: newContent,
              language: newLanguage,
              // Clear result data when converting a code cell to markdown
              result: targetType === 'markdown' ? undefined : c.result,
              error: targetType === 'markdown' ? undefined : c.error,
              errorDetails: targetType === 'markdown' ? undefined : c.errorDetails
            }
          : c
      );
    });

    toast.success(`Cell converted to ${targetType} cell`);
  };

  return (
    <div className="flex h-screen">
      {/* MiniSidebar */}
      <MiniSidebar
      // @ts-ignore
        savedDatasets={datasets}
        storageInfo={{
          used: datasets.reduce((acc, dataset) => acc + (dataset.data?.length || 0) * 0.001, 0),
          total: 100,
          percentage: datasets.length > 0 ? Math.min(datasets.reduce((acc, dataset) => acc + (dataset.data?.length || 0) * 0.001, 0), 100) : 0
        }}
        isLoadingDatasets={isLoadingDatasets}
        onDatasetSelect={(dataset) => {
          // Find the first cell and select this dataset for it
          if (cells.length > 0) {
            const firstCellId = cells[0].id;
            handleSelectDatasets(firstCellId, [dataset.id]).then(result => {
              if (result && result.datasetIds) {
                // Update the cell's selectedDatasetIds
                setCells(prev => prev.map(c =>
                  c.id === firstCellId ? { ...c, selectedDatasetIds: result.datasetIds } : c
                ));
              }
            }).catch(err => console.error('Error selecting datasets:', err));
          }
        }}
        onDeleteDataset={(datasetId) => {
          toast.success('Dataset removed');
        }}
        onShowVersionHistory={(dataset) => {
          toast.info(`Version history for ${dataset.name} is not available`);
        }}
        onUploadClick={() => {
          toast.info('Upload functionality is not available in this view');
        }}
        isCollapsed={isSidebarCollapsed}
        onToggleCollapse={() => setIsSidebarCollapsed(prev => !prev)}
      />

      <div className={`flex-1 overflow-auto transition-all ${isSidebarCollapsed ? 'ml-12' : 'ml-64'}`}>
        <div className="container mx-auto max-w-[1200px] px-2">
          {!isOnline && (
            <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-3 py-2 rounded-md mb-3 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="h-2 w-2 rounded-full bg-yellow-500 animate-pulse"></span>
                <span className="text-sm font-medium">Offline Mode - Using Mock Data</span>
              </div>
              <div className="text-xs">
                Only SQL queries are available in offline mode.
              </div>
            </div>
          )}

          <Tabs
            value={activeTab}
            onValueChange={(value) => {
              // Update URL when tab changes
              const params = new URLSearchParams(searchParams.toString())
              if (value === 'dashboard') {
                params.set('tab', 'dashboard')
              } else {
                params.delete('tab')
              }

              // Update active tab state
              setActiveTab(value);

              // Update URL without scrolling
              router.push(`?${params.toString()}`, { scroll: false })

              // Force a resize event after tab change to ensure charts render properly
              setTimeout(() => {
                if (typeof window !== 'undefined') {
                  window.dispatchEvent(new Event('resize'));
                }
              }, 100);
            }}
            className="w-full">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <h1 className="text-xl font-bold">SQL Notebook</h1>
            <TabsList className="h-8">
              <TabsTrigger value="notebook" className="text-xs px-2 h-8">Notebook</TabsTrigger>
              <TabsTrigger
                value="dashboard"
                className="text-xs px-2 h-8"
              >
                Dashboard
              </TabsTrigger>
            </TabsList>
            {Object.keys(datasetCache).length > 0 && activeTab === "notebook" && (
              <div className="text-xs text-muted-foreground flex items-center gap-1">
                <Database className="h-3 w-3" />
                <span>Using {Object.keys(datasetCache).length} dataset(s)</span>
              </div>
            )}

            <NotebookExporter
              cells={cells}
              savedCharts={savedCharts}
              selectedDatasets={Object.values(datasetCache)}
              onImport={handleNotebookImport}
            />
          </div>
          {activeTab === "notebook" && (
            <div className="relative">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    className="h-7 text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add Cell
                    <ChevronDown className="h-3 w-3 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleAddCell(cells.length > 0 ? cells[cells.length - 1].id : undefined, 'code')}>
                    <Code2 className="h-4 w-4 mr-2" />
                    <span>Code Cell</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleAddCell(cells.length > 0 ? cells[cells.length - 1].id : undefined, 'markdown')}>
                    <FileText className="h-4 w-4 mr-2" />
                    <span>Markdown Cell</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>

        <TabsContent value="notebook" className="mt-0">
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="notebook-cells">
              {(provided) => (
                <div
                  className="space-y-4"
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                >
                  {cells.map((cell, index) => (
                    <Draggable key={cell.id} draggableId={cell.id} index={index}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`${snapshot.isDragging ? 'opacity-70' : ''}`}
                        >
                          <Cell
                            key={cell.id}
                            id={cell.id}
                            content={cell.content}
                            language={cell.language}
                            cellType={cell.cellType}
                            result={{
                              data: cell.result?.data || [],
                              output: cell.result?.output,
                              plots: cell.result?.plots,
                              error: cell.error,
                              errorDetails: cell.errorDetails,
                              executionTime: cell.executionTime
                            }}
                            onRun={handleRunCell}
                            onDelete={handleDeleteCell}
                            onAddCell={handleAddCell}
                            onSelectDatasets={(datasetIds) => {
                              return new Promise<{ selectedList: Dataset[], datasetIds: string[] }>((resolve, reject) => {
                                handleSelectDatasets(cell.id, datasetIds).then(result => {
                                  if (result && result.datasetIds) {
                                    // Update the cell's selectedDatasetIds
                                    setCells(prev => prev.map(c =>
                                      c.id === cell.id ? { ...c, selectedDatasetIds: result.datasetIds } : c
                                    ));
                                    resolve(result);
                                  } else {
                                    resolve({ selectedList: [], datasetIds: [] });
                                  }
                                }).catch(err => {
                                  console.error('Error selecting datasets:', err);
                                  reject(err);
                                });
                              });
                            }}
                            onLanguageChange={(lang) => handleLanguageChange(cell.id, lang)}
                            selectedDatasets={(cell.selectedDatasetIds || []).map(id => datasetCache[id]).filter(Boolean)}
                            availableDatasets={datasets}
                            isSuccess={cell.isSuccess}
                            showGraphicWalker={cell.showGraphicWalker || false}
                            onSaveChart={handleSaveChart}
                            onSaveTable={handleSaveTable}
                            onSavePlot={handleSavePlot}
                            notes={cell.notes || '[]'}
                            onUpdateNotes={handleUpdateNotes}
                            index={index} // Pass the index to show cell number
                            dragHandleProps={provided.dragHandleProps} // Pass drag handle props
                            onMoveUp={moveCellUp} // Pass move up function
                            onMoveDown={moveCellDown} // Pass move down function
                            onContentChange={(value) => {
                              setCells(prev => prev.map(c =>
                                c.id === cell.id ? { ...c, content: value } : c
                              ));
                            }}
                            onConvertCellType={convertCellType} // Pass the convert cell type function
                          />
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </TabsContent>

        <TabsContent value="dashboard" className="mt-0">
          <Dashboard
            charts={savedCharts}
            items={[
              ...savedCharts,
              ...savedTables,
              ...savedPlots,
              ...savedCalculatorResults
            ]}
            onRemoveItem={(itemId) => {
              // Determine the type of item and call the appropriate handler
              if (itemId.startsWith('chart-')) {
                handleRemoveChart(itemId);
              } else if (itemId.startsWith('table-')) {
                useDashboardStore.getState().removeTable(itemId);
              } else if (itemId.startsWith('plot-')) {
                useDashboardStore.getState().removePlot(itemId);
              } else if (itemId.startsWith('calc-')) {
                useDashboardStore.getState().removeCalculatorResult(itemId);
              }
            }}
            onUpdateItem={(itemId, updates) => {
              // Determine the type of item and call the appropriate handler
              if (itemId.startsWith('chart-')) {
                // Cast updates to Partial<SavedChart> to ensure type compatibility
                handleUpdateChart(itemId, updates as unknown as Partial<SavedChart>);
              } else if (itemId.startsWith('table-')) {
                // Use the Zustand store's updateTable function
                useDashboardStore.getState().updateTable(itemId, updates as Partial<TableItem>);
              } else if (itemId.startsWith('plot-')) {
                // Use the Zustand store's updatePlot function
                useDashboardStore.getState().updatePlot(itemId, updates as Partial<PythonPlotItem>);
              } else if (itemId.startsWith('calc-')) {
                // Use the Zustand store's updateCalculatorResult function
                useDashboardStore.getState().updateCalculatorResult(itemId, updates);
              }
            }}
            onReorderItems={(newItems) => {
              // Extract charts, tables, plots, and calculator results from the new items with proper type casting
              const charts = newItems.filter(item => item.type === 'chart') as SavedChart[];
              const tables = newItems.filter(item => item.type === 'table') as TableItem[];
              const plots = newItems.filter(item => item.type === 'pythonplot') as PythonPlotItem[];
              const calculatorResults = newItems.filter(item => item.type === 'calculator');

              // Use the Zustand store to update all items
              const store = useDashboardStore.getState();

              // Clear current items and add the reordered ones
              store.clearAll();

              // Add all items back with their new positions
              charts.forEach(chart => store.addChart(chart));
              tables.forEach(table => store.addTable(table));
              plots.forEach(plot => store.addPlot(plot));
              calculatorResults.forEach(calc => store.addCalculatorResult(calc));
            }}
            onRemoveChart={handleRemoveChart}
            onUpdateChart={handleUpdateChart}
            onReorderCharts={handleReorderCharts}
            onAddChart={(chart) => {
              // Ensure the chart has all required properties
              const validChart: SavedChart = {
                ...chart,
                type: 'chart',
                title: chart.title || 'Untitled Chart',
                description: chart.description || '',
                gridColumn: chart.gridColumn || 0,
                gridRow: chart.gridRow || 0,
                width: chart.width || 4,
                height: chart.height || 3,
                chartType: chart.chartType || 'bar',
                data: chart.data || [],
                config: chart.config || {},
                createdAt: chart.createdAt || new Date()
              };
              // Add to the Zustand store
              useDashboardStore.getState().addChart(validChart);
            }}
          />
        </TabsContent>
      </Tabs>
        </div>
      </div>
    </div>
  )
}

