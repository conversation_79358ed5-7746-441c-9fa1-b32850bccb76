import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Dataset } from '@/types/index';
import { UseDatasetHandlingReturn } from './types';
import { checkInternetConnection } from './chartBuilderUtils';
import { mockDatasets, getAllMockDatasets } from '@/utils/mockData';

/**
 * Hook for handling datasets in the ChartBuilder
 * @returns Dataset handling functions and state
 */
export const useDatasetHandling = (): UseDatasetHandlingReturn => {
  // Store all loaded datasets in a cache for quick access
  const [datasetCache, setDatasetCache] = useState<Record<string, Dataset>>({});
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(true);

  // Function to fetch datasets (can be called manually)
  const fetchDatasets = async () => {
      try {
        setIsLoadingDatasets(true);
        // Check for internet connection first
        const online = await checkInternetConnection();

        if (online) {
          // Fetch datasets and folders from API
          const [datasetsResponse, foldersResponse] = await Promise.all([
            fetch('/api/datasets'),
            fetch('/api/datasets/folders')
          ]);

          // Check authentication for both requests
          if (datasetsResponse.status === 401 || foldersResponse.status === 401) {
            console.warn('Authentication required - user not signed in, using mock data');
            throw new Error('Authentication required - using mock data');
          }

          if (!datasetsResponse.ok || !foldersResponse.ok) {
            console.error(`API Error: Datasets ${datasetsResponse.status}, Folders ${foldersResponse.status}`);
            throw new Error(`Failed to fetch data: ${datasetsResponse.status}/${foldersResponse.status}`);
          }

          const [datasetsData, foldersData] = await Promise.all([
            datasetsResponse.json(),
            foldersResponse.json()
          ]);

          console.log('API Responses:', { datasetsData, foldersData });

          // Collect all datasets from both root and folders
          const allDatasets: any[] = [];

          // Add root datasets (not in folders)
          if (datasetsData.success && Array.isArray(datasetsData.datasets)) {
            allDatasets.push(...datasetsData.datasets);
          }

          // Add datasets from folders
          if (foldersData.success) {
            // Add root datasets from folders response
            if (Array.isArray(foldersData.datasets)) {
              allDatasets.push(...foldersData.datasets);
            }

            // Add datasets from each folder
            if (Array.isArray(foldersData.folders)) {
              foldersData.folders.forEach((folder: any) => {
                if (Array.isArray(folder.datasets)) {
                  allDatasets.push(...folder.datasets);
                }
              });
            }
          }

          // Remove duplicates based on ID
          const uniqueDatasets = allDatasets.filter((dataset, index, self) =>
            index === self.findIndex(d => d.id === dataset.id)
          );

          if (uniqueDatasets.length > 0) {
            // Transform API data to match our Dataset interface
            const transformedDatasets = uniqueDatasets.map((ds: any) => ({
              id: ds.id,
              name: ds.name,
              data: ds.data || [],
              columns: ds.headers?.map((header: string) => ({ name: header, type: 'string' })) || [],
              headers: ds.headers || [],
              fileType: ds.fileType || 'json',
              createdAt: new Date(ds.createdAt),
              description: ds.description
            }));

            setDatasets(transformedDatasets);
            console.log(`Loaded ${transformedDatasets.length} datasets from API (including folders)`);

            // Also populate the cache with real data
            const realCache: Record<string, Dataset> = {};
            transformedDatasets.forEach(ds => {
              realCache[ds.id] = ds;
            });
            setDatasetCache(realCache);

            toast.success(`Loaded ${transformedDatasets.length} datasets from database`);
            return; // Exit early on success
          } else {
            console.warn('No datasets found in API response');
            throw new Error('No datasets found');
          }
        } else {
          // Use mock datasets in offline mode
          throw new Error('No internet connection, using mock data');
        }
      } catch (error) {
        console.warn('Using mock datasets:', error);

        // Load mock datasets
        const mockData = getAllMockDatasets();
        setDatasets(mockData.map(ds => ({
          id: ds.id,
          name: ds.name,
          data: ds.data,
          columns: Object.keys(ds.data[0] || {}).map(key => ({ name: key, type: 'string' })),
          headers: Object.keys(ds.data[0] || {}),
          fileType: 'json',
          createdAt: new Date(ds.createdAt)
        })));

        // Also initialize the dataset cache with mock data to skip network requests later
        const mockCache: Record<string, Dataset> = {};
        mockData.forEach(ds => {
          mockCache[ds.id] = {
            id: ds.id,
            name: ds.name,
            data: ds.data,
            columns: Object.keys(ds.data[0] || {}).map(key => ({ name: key, type: 'string' })),
            headers: Object.keys(ds.data[0] || {}),
            fileType: 'json',
            createdAt: new Date(ds.createdAt)
          };
        });
        setDatasetCache(mockCache);

        toast.info(`Using offline mock datasets (${mockData.length} available)`);
      } finally {
        setIsLoadingDatasets(false);
      }
    };

  // Fetch datasets on component mount
  useEffect(() => {
    fetchDatasets();
  }, []);

  // Handle dataset selection for a specific cell
  const handleSelectDatasets = async (cellId: string, datasetIds: string[]) => {
    try {
      const selectedList: Dataset[] = [];
      const newCache = { ...datasetCache };

      // Fetch each selected dataset
      for (const datasetId of datasetIds) {
        // Check if we already have this dataset in the cache
        if (newCache[datasetId]) {
          selectedList.push(newCache[datasetId]);
          continue;
        }

        // Check internet connection
        const isOnline = await checkInternetConnection();

        if (isOnline) {
          // Fetch from API
          const response = await fetch(`/api/datasets?datasetId=${datasetId}`);

          if (response.status === 401) {
            console.warn(`Authentication required for dataset ${datasetId}, using mock data`);
            // Fall through to mock data handling
          } else if (!response.ok) {
            console.warn(`Failed to load dataset ${datasetId} from API (${response.status}), using mock data`);
            // Fall through to mock data handling
          } else {
            const data = await response.json();
            if (data.success && data.datasetInfo) {
              const dataset: Dataset = {
                id: datasetId,
                name: data.datasetInfo.name,
                data: data.datasetInfo.data,
                columns: data.datasetInfo.headers?.map((header: string) => ({ name: header, type: 'string' })) || [],
                headers: data.datasetInfo.headers,
                fileType: data.datasetInfo.fileType,
                createdAt: new Date(data.datasetInfo.createdAt)
              };
              selectedList.push(dataset);
              newCache[datasetId] = dataset;
              continue; // Skip to next dataset
            } else {
              console.warn(`Invalid dataset response for ${datasetId}, using mock data`);
              // Fall through to mock data handling
            }
          }
        }

        // Use mock dataset (either offline or auth failed)
        const mockDs = mockDatasets.find(ds => ds.id === datasetId);
        if (mockDs) {
          const dataset: Dataset = {
            id: datasetId,
            name: mockDs.name,
            data: mockDs.data,
            columns: Object.keys(mockDs.data[0] || {}).map(key => ({ name: key, type: 'string' })),
            headers: Object.keys(mockDs.data[0] || {}),
            fileType: 'json',
            createdAt: new Date(mockDs.createdAt)
          };
          selectedList.push(dataset);
          newCache[datasetId] = dataset;
        } else {
          toast.error(`Dataset ${datasetId} not found`);
        }
      }

      // Update the dataset cache with any new datasets
      setDatasetCache(newCache);

      if (selectedList.length > 0) {
        toast.success(`${selectedList.length} dataset(s) selected for cell`);
      }

      return { selectedList, datasetIds };
    } catch (error) {
      console.error('Failed to load datasets:', error);
      toast.error('Failed to load one or more datasets');
      throw error;
    }
  };

  return {
    datasets,
    datasetCache,
    isLoadingDatasets,
    handleSelectDatasets,
    refreshDatasets: fetchDatasets
  };
};
